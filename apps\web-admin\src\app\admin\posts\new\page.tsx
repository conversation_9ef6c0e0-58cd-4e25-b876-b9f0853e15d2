'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { AdminDashboard } from '@/components/AdminDashboard';
import { PostEditor } from '@/components/cms/PostEditor';

export default function NewPostPage() {
  const router = useRouter();

  const handleSave = (_post: unknown) => {
    // Redirect to posts list or edit page
    router.push('/admin/posts');
  };

  const handleCancel = () => {
    router.push('/admin/posts');
  };

  return (
    <AuthGuard>
      <AdminDashboard>
        <PostEditor
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </AdminDashboard>
    </AuthGuard>
  );
}
