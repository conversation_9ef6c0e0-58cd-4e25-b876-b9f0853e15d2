'use client';

import React from 'react';
import { usePathname } from 'next/navigation';
import { SidebarProps } from '@/types';
import { SidebarItem } from '@/components/ui';

/**
 * Admin Sidebar component with navigation items
 *
 * @param isOpen - Whether the sidebar is currently open
 * @param onToggle - Function to toggle sidebar state
 */
export function Sidebar({ isOpen, onToggle: _onToggle }: SidebarProps) {
  const pathname = usePathname();
  return (
    <aside 
      className={`fixed left-0 bg-white border-r border-gray-200 transition-all duration-300 overflow-y-auto z-40 ${
        isOpen ? 'w-64' : 'w-16'
      }`} 
      style={{
        height: 'calc(100vh - 4rem)',
        scrollbarWidth: 'thin',
        scrollbarColor: '#cbd5e1 transparent'
      }}
    >
      <div className="p-3">
        <nav className="space-y-1">
          {/* Main Navigation */}
          <div className="space-y-1">
            <SidebarItem
              icon="dashboard"
              label="Dashboard"
              active={pathname === '/admin'}
              collapsed={!isOpen}
              href="/admin"
            />
            <SidebarItem
              icon="posts"
              label="Blog Posts"
              active={pathname.startsWith('/admin/posts')}
              collapsed={!isOpen}
              href="/admin/posts"
            />
            <SidebarItem
              icon="settings"
              label="Settings"
              active={pathname.startsWith('/admin/settings')}
              collapsed={!isOpen}
              href="/admin/settings"
            />
          </div>
        </nav>
      </div>
    </aside>
  );
}
