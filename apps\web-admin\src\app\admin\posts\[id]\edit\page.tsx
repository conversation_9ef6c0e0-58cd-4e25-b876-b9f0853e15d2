'use client';

import React from 'react';
import { useRouter, useParams } from 'next/navigation';
import { AuthGuard } from '@/components/auth/AuthGuard';
import { AdminDashboard } from '@/components/AdminDashboard';
import { PostEditor } from '@/components/cms/PostEditor';

export default function EditPostPage() {
  const router = useRouter();
  const params = useParams();
  const postId = params.id as string;

  const handleSave = (_post: unknown) => {
    // Redirect to posts list
    router.push('/admin/posts');
  };

  const handleCancel = () => {
    router.push('/admin/posts');
  };

  return (
    <AuthGuard>
      <AdminDashboard>
        <PostEditor
          postId={postId}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </AdminDashboard>
    </AuthGuard>
  );
}
