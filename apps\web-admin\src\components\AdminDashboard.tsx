"use client";

import React from "react";
import { Header, Sidebar } from "@/components/layout";
import { useSidebar } from "@/hooks";

interface AdminDashboardProps {
  children?: React.ReactNode;
}

/**
 * Admin Dashboard component - Main admin layout
 *
 * This component orchestrates the main admin layout with header, sidebar, and content areas.
 * It uses custom hooks for state management and follows the same structure as the main web app.
 */
export function AdminDashboard({ children }: AdminDashboardProps) {
  const { isOpen: sidebarOpen, toggle: toggleSidebar } = useSidebar(true);

  const handleSearch = (query: string) => {
    console.log('Admin search query:', query);
    // TODO: Implement admin search functionality
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <Header
        sidebarOpen={sidebarOpen}
        onToggleSidebar={toggleSidebar}
        onSearch={handleSearch}
      />

      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        onToggle={toggleSidebar}
      />

      {/* Main Content */}
      <main className={`transition-all duration-300 ${sidebarOpen ? 'ml-64' : 'ml-16'}`}>
        {children || <DefaultDashboardContent />}
      </main>
    </div>
  );
}

/**
 * Default dashboard content when no children are provided
 */
function DefaultDashboardContent() {
  return (
    <div className="p-6">
      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Contacts</h3>
          <p className="text-2xl font-bold text-gray-900">42</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500">New Contacts</h3>
          <p className="text-2xl font-bold text-blue-600">7</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500">Total Subscribers</h3>
          <p className="text-2xl font-bold text-gray-900">156</p>
        </div>
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-sm font-medium text-gray-500">Active Subscribers</h3>
          <p className="text-2xl font-bold text-green-600">134</p>
        </div>
      </div>

      {/* Welcome Message */}
      <div className="bg-white rounded-lg shadow p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">
          Welcome to Admin Dashboard
        </h2>
        <p className="text-gray-600">
          You are now authenticated and have access to the admin control panel.
        </p>
      </div>
    </div>
  );
}




